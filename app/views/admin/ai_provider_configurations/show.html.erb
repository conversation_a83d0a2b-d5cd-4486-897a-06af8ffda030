<% content_for :title, "#{@ai_provider_configuration.provider_name.titleize} Configuration" %>

<!-- Modern AI Provider Configuration Show Page -->
<div class="min-h-screen bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

    <!-- Breadcrumb Navigation -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
      <ol role="list" class="flex items-center space-x-4">
        <li>
          <%= link_to admin_ai_provider_configurations_path,
              class: "text-gray-500 hover:text-gray-700 transition-colors duration-200 flex items-center" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            <span class="text-sm font-medium">AI Provider Configurations</span>
          <% end %>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-4 w-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="text-sm font-medium text-gray-900"><%= @ai_provider_configuration.provider_name.titleize %></span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- Page Header with Provider Info -->
    <div class="bg-white rounded-xl shadow-sm p-6 mb-8">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="flex items-start space-x-4 mb-4 lg:mb-0">
          <!-- Provider Icon -->
          <div class="flex-shrink-0">
            <div class="w-16 h-16 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl flex items-center justify-center">
              <% case @ai_provider_configuration.provider_name.downcase %>
              <% when 'openai' %>
                <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z"/>
                </svg>
              <% when 'anthropic' %>
                <svg class="w-8 h-8 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                </svg>
              <% else %>
                <svg class="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                </svg>
              <% end %>
            </div>
          </div>

          <!-- Provider Details -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center space-x-3 mb-2">
              <h1 class="text-2xl font-bold text-gray-900 truncate">
                <%= @ai_provider_configuration.provider_name.titleize %>
              </h1>
              <!-- Real-time Status Indicator -->
              <div class="flex items-center space-x-2">
                <% if @ai_provider_configuration.provider_available? %>
                  <div class="relative">
                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                    <div class="absolute inset-0 w-3 h-3 bg-green-400 rounded-full animate-ping opacity-75"></div>
                  </div>
                  <span class="text-sm font-medium text-green-700">Available</span>
                <% else %>
                  <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                  <span class="text-sm font-medium text-red-700">Not Configured</span>
                <% end %>
              </div>
            </div>

            <div class="flex flex-wrap items-center gap-3 mb-3">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                <%= @ai_provider_configuration.ai_model_name %>
              </span>

              <% if @ai_provider_configuration.is_active? %>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                  <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                    <circle cx="4" cy="4" r="3"/>
                  </svg>
                  Active
                </span>
              <% else %>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                  <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-gray-400" fill="currentColor" viewBox="0 0 8 8">
                    <circle cx="4" cy="4" r="3"/>
                  </svg>
                  Inactive
                </span>
              <% end %>

              <% if @ai_provider_configuration.default_for_task_type.present? %>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                  Default for <%= @ai_provider_configuration.default_for_task_type.humanize %>
                </span>
              <% end %>
            </div>

            <p class="text-gray-600 text-sm">
              Advanced AI model configuration with real-time monitoring and performance analytics
            </p>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-3 lg:flex-shrink-0">
          <%= link_to test_connection_admin_ai_provider_configuration_path(@ai_provider_configuration),
              data: { turbo_method: :post },
              class: "inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200" do %>
            <svg class="-ml-1 mr-2 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
            </svg>
            Test Connection
          <% end %>

          <%= link_to edit_admin_ai_provider_configuration_path(@ai_provider_configuration),
              class: "inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200" do %>
            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
            </svg>
            Edit Configuration
          <% end %>

          <%= link_to toggle_active_admin_ai_provider_configuration_path(@ai_provider_configuration),
              data: {
                turbo_method: :patch,
                turbo_confirm: @ai_provider_configuration.is_active? ? "Are you sure you want to deactivate this provider?" : "Are you sure you want to activate this provider?"
              },
              class: "inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg shadow-sm transition-all duration-200 #{ @ai_provider_configuration.is_active? ? 'text-red-700 bg-red-50 hover:bg-red-100 focus:ring-red-500' : 'text-green-700 bg-green-50 hover:bg-green-100 focus:ring-green-500' } focus:outline-none focus:ring-2 focus:ring-offset-2" do %>
            <% if @ai_provider_configuration.is_active? %>
              <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              Deactivate
            <% else %>
              <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              Activate
            <% end %>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

      <!-- Configuration Details - Left Column (2/3 width) -->
      <div class="lg:col-span-2 space-y-6">

        <!-- Core Configuration Card -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Core Configuration</h3>
              <p class="text-sm text-gray-600 mt-1">Essential settings and parameters for this AI provider</p>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
              </svg>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Provider Info -->
            <div class="space-y-4">
              <div>
                <label class="text-sm font-medium text-gray-500">Provider</label>
                <div class="mt-1 flex items-center space-x-2">
                  <span class="text-sm font-semibold text-gray-900"><%= @ai_provider_configuration.provider_name.titleize %></span>
                  <% if @ai_provider_configuration.provider_available? %>
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                      Configured
                    </span>
                  <% else %>
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                      Not Configured
                    </span>
                  <% end %>
                </div>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500">Model Name</label>
                <p class="mt-1 text-sm font-semibold text-gray-900"><%= @ai_provider_configuration.ai_model_name %></p>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500">Base URL</label>
                <p class="mt-1 text-sm text-gray-900 font-mono bg-gray-50 px-3 py-2 rounded-lg">
                  <%= @ai_provider_configuration.base_url %>
                </p>
              </div>
            </div>

            <!-- Model Parameters -->
            <div class="space-y-4">
              <div>
                <label class="text-sm font-medium text-gray-500">Max Tokens</label>
                <div class="mt-1 flex items-center space-x-2">
                  <span class="text-sm font-semibold text-gray-900"><%= number_with_delimiter(@ai_provider_configuration.max_tokens) %></span>
                  <span class="text-xs text-gray-500">tokens</span>
                </div>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500">Temperature</label>
                <div class="mt-1 flex items-center space-x-3">
                  <span class="text-sm font-semibold text-gray-900"><%= @ai_provider_configuration.temperature %></span>
                  <div class="flex-1 bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full" style="width: <%= (@ai_provider_configuration.temperature * 100).round(1) %>%"></div>
                  </div>
                  <span class="text-xs text-gray-500">creativity</span>
                </div>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500">Cost per Token</label>
                <div class="mt-1 flex items-center space-x-2">
                  <span class="text-sm font-semibold text-gray-900">$<%= @ai_provider_configuration.cost_per_token %></span>
                  <span class="text-xs text-gray-500">USD</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Advanced Settings Card -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Advanced Settings</h3>
              <p class="text-sm text-gray-600 mt-1">Priority, task assignments, and security configuration</p>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
              </svg>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Priority & Tasks -->
            <div class="space-y-4">
              <div>
                <label class="text-sm font-medium text-gray-500">Priority Level</label>
                <div class="mt-1 flex items-center space-x-3">
                  <span class="text-sm font-semibold text-gray-900"><%= @ai_provider_configuration.priority %></span>
                  <div class="flex-1 bg-gray-200 rounded-full h-2">
                    <div class="bg-gradient-to-r from-green-500 to-red-500 h-2 rounded-full" style="width: <%= (11 - @ai_provider_configuration.priority) * 10 %>%"></div>
                  </div>
                  <span class="text-xs text-gray-500">(1 = highest)</span>
                </div>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500">Supported Tasks</label>
                <div class="mt-2">
                  <% if @ai_provider_configuration.task_types.any? %>
                    <div class="flex flex-wrap gap-2">
                      <% @ai_provider_configuration.task_types.each do |task_type| %>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          <%= task_type.humanize %>
                        </span>
                      <% end %>
                    </div>
                  <% else %>
                    <span class="text-sm text-gray-500 italic">No specific tasks configured</span>
                  <% end %>
                </div>
              </div>
            </div>

            <!-- Security & Access -->
            <div class="space-y-4">
              <div>
                <label class="text-sm font-medium text-gray-500">API Key Status</label>
                <div class="mt-1 flex items-center space-x-3">
                  <span class="font-mono text-xs bg-gray-100 px-3 py-1 rounded">••••••••••••••••</span>
                  <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                    </svg>
                    Encrypted
                  </span>
                </div>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500">Created</label>
                <p class="mt-1 text-sm text-gray-900">
                  <%= time_ago_in_words(@ai_provider_configuration.created_at) %> ago
                  <span class="text-gray-500">(<%= @ai_provider_configuration.created_at.strftime("%B %d, %Y") %>)</span>
                </p>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500">Last Updated</label>
                <p class="mt-1 text-sm text-gray-900">
                  <%= time_ago_in_words(@ai_provider_configuration.updated_at) %> ago
                  <span class="text-gray-500">(<%= @ai_provider_configuration.updated_at.strftime("%B %d, %Y") %>)</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>


      <!-- Right Sidebar - Usage Statistics & Quick Actions -->
      <div class="lg:col-span-1 space-y-6">

        <!-- Quick Actions Card -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
            </svg>
          </div>

          <div class="space-y-3">
            <!-- Connection Test -->
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"/>
                  </svg>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">Test Connection</p>
                  <p class="text-xs text-gray-500">Verify API connectivity</p>
                </div>
              </div>
              <%= link_to test_connection_admin_ai_provider_configuration_path(@ai_provider_configuration),
                  data: { turbo_method: :post },
                  class: "text-blue-600 hover:text-blue-700 text-sm font-medium" do %>
                Test
              <% end %>
            </div>

            <!-- Toggle Status -->
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-<%= @ai_provider_configuration.is_active? ? 'red' : 'green' %>-100 rounded-lg flex items-center justify-center">
                  <% if @ai_provider_configuration.is_active? %>
                    <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  <% else %>
                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  <% end %>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">
                    <%= @ai_provider_configuration.is_active? ? 'Deactivate' : 'Activate' %>
                  </p>
                  <p class="text-xs text-gray-500">
                    <%= @ai_provider_configuration.is_active? ? 'Disable this provider' : 'Enable this provider' %>
                  </p>
                </div>
              </div>
              <%= link_to toggle_active_admin_ai_provider_configuration_path(@ai_provider_configuration),
                  data: {
                    turbo_method: :patch,
                    turbo_confirm: @ai_provider_configuration.is_active? ? "Deactivate this provider?" : "Activate this provider?"
                  },
                  class: "text-#{@ai_provider_configuration.is_active? ? 'red' : 'green'}-600 hover:text-#{@ai_provider_configuration.is_active? ? 'red' : 'green'}-700 text-sm font-medium" do %>
                <%= @ai_provider_configuration.is_active? ? 'Disable' : 'Enable' %>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Usage Statistics Card -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Usage Analytics</h3>
              <p class="text-sm text-gray-600 mt-1">Performance metrics and cost tracking</p>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
            </div>
          </div>

          <div class="space-y-6">
            <!-- Total Requests -->
            <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl">
              <div class="text-3xl font-bold text-blue-600 mb-1">
                <%= number_with_delimiter(@usage_stats[:total_requests]) %>
              </div>
              <div class="text-sm text-gray-600">Total Requests</div>
              <div class="text-xs text-gray-500 mt-1">All time</div>
            </div>

            <!-- Success Rate -->
            <div>
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">Success Rate</span>
                <% success_rate = (@usage_stats[:successful_requests].to_f / @usage_stats[:total_requests] * 100).round(1) %>
                <span class="text-sm font-bold text-green-600"><%= success_rate %>%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-3">
                <div class="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full transition-all duration-500"
                     style="width: <%= success_rate %>%"></div>
              </div>
              <div class="flex justify-between text-xs text-gray-500 mt-1">
                <span><%= number_with_delimiter(@usage_stats[:successful_requests]) %> successful</span>
                <span><%= number_with_delimiter(@usage_stats[:failed_requests]) %> failed</span>
              </div>
            </div>

            <!-- Response Time -->
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p class="text-sm font-medium text-gray-700">Avg Response Time</p>
                <p class="text-xs text-gray-500">Last 30 days</p>
              </div>
              <div class="text-right">
                <p class="text-lg font-bold text-gray-900"><%= @usage_stats[:average_response_time] %></p>
                <p class="text-xs text-gray-500">milliseconds</p>
              </div>
            </div>

            <!-- Total Cost -->
            <div class="flex items-center justify-between p-3 bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg">
              <div>
                <p class="text-sm font-medium text-gray-700">Total Cost</p>
                <p class="text-xs text-gray-500">All time usage</p>
              </div>
              <div class="text-right">
                <p class="text-lg font-bold text-purple-600">$<%= sprintf('%.2f', @usage_stats[:total_cost]) %></p>
                <p class="text-xs text-gray-500">USD</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Provider Health Card -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Provider Health</h3>
            <div class="flex items-center space-x-2">
              <% if @ai_provider_configuration.provider_available? %>
                <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span class="text-sm font-medium text-green-700">Healthy</span>
              <% else %>
                <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                <span class="text-sm font-medium text-red-700">Issues</span>
              <% end %>
            </div>
          </div>

          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">API Connection</span>
              <% if @ai_provider_configuration.provider_available? %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  Connected
                </span>
              <% else %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                  </svg>
                  Disconnected
                </span>
              <% end %>
            </div>

            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">Configuration</span>
              <% if @ai_provider_configuration.is_active? %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                  Active
                </span>
              <% else %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                  Inactive
                </span>
              <% end %>
            </div>

            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">Last Check</span>
              <span class="text-xs text-gray-500">
                <%= time_ago_in_words(@ai_provider_configuration.updated_at) %> ago
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
