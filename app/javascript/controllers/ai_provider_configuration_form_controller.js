import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="ai-provider-configuration-form"
export default class extends Controller {
  static targets = [
    "providerSelect", "modelInput", "apiKeyInput", "baseUrlInput",
    "maxTokensInput", "temperatureInput", "costInput", "priorityInput",
    "activeCheckbox", "saveButton", "temperatureBar", "priorityBar",
    "previewModal", "previewContent"
  ]
  
  static values = { 
    provider: String,
    model: String
  }

  connect() {
    console.log("AI Provider Configuration Form controller connected")
    this.initializeForm()
    this.updateVisualizations()
  }

  // Initialize form with current values
  initializeForm() {
    this.updateProviderDefaults()
    this.validateForm()
  }

  // Provider selection changed
  providerChanged(event) {
    const provider = event.target.value
    this.providerValue = provider
    this.updateProviderDefaults()
    this.validateForm()
    this.showFeedback(`Provider changed to ${provider}`, "info")
  }

  // Model name changed
  modelChanged(event) {
    const model = event.target.value
    this.modelValue = model
    this.updateModelDefaults()
    this.validateForm()
  }

  // API key changed
  apiKeyChanged(event) {
    this.validateApiKey(event.target.value)
    this.validateForm()
  }

  // Base URL changed
  baseUrlChanged(event) {
    this.validateBaseUrl(event.target.value)
    this.validateForm()
  }

  // Max tokens changed
  maxTokensChanged(event) {
    this.validateMaxTokens(event.target.value)
    this.validateForm()
  }

  // Temperature changed
  temperatureChanged(event) {
    const temperature = parseFloat(event.target.value)
    this.updateTemperatureVisualization(temperature)
    this.validateForm()
  }

  // Cost changed
  costChanged(event) {
    this.validateCost(event.target.value)
    this.validateForm()
  }

  // Priority changed
  priorityChanged(event) {
    const priority = parseInt(event.target.value)
    this.updatePriorityVisualization(priority)
    this.validateForm()
  }

  // Active status changed
  activeStatusChanged(event) {
    const isActive = event.target.checked
    this.showFeedback(`Configuration ${isActive ? 'activated' : 'deactivated'}`, "info")
    this.validateForm()
  }

  // Update provider-specific defaults
  updateProviderDefaults() {
    const provider = this.providerValue
    
    // Update model suggestions based on provider
    if (this.hasModelInputTarget) {
      const modelInput = this.modelInputTarget
      const currentModel = modelInput.value
      
      if (!currentModel || currentModel === "") {
        const defaultModels = {
          'openai': 'gpt-4o',
          'anthropic': 'claude-3-sonnet-20240229',
          'gemini': 'gemini-1.5-pro',
          'deepseek': 'deepseek-chat',
          'openrouter': 'openai/gpt-4o'
        }
        
        if (defaultModels[provider]) {
          modelInput.value = defaultModels[provider]
          this.modelValue = defaultModels[provider]
        }
      }
    }

    // Update base URL placeholder
    if (this.hasBaseUrlInputTarget) {
      const baseUrlInput = this.baseUrlInputTarget
      const defaultUrls = {
        'openai': 'https://api.openai.com/v1',
        'anthropic': 'https://api.anthropic.com',
        'gemini': 'https://generativelanguage.googleapis.com/v1',
        'deepseek': 'https://api.deepseek.com/v1',
        'openrouter': 'https://openrouter.ai/api/v1'
      }
      
      if (defaultUrls[provider]) {
        baseUrlInput.placeholder = defaultUrls[provider]
      }
    }
  }

  // Update model-specific defaults
  updateModelDefaults() {
    const model = this.modelValue.toLowerCase()
    
    // Update max tokens based on model
    if (this.hasMaxTokensInputTarget) {
      const maxTokensInput = this.maxTokensInputTarget
      const currentMaxTokens = maxTokensInput.value
      
      if (!currentMaxTokens || currentMaxTokens === "") {
        let defaultMaxTokens = 4096
        
        if (model.includes('gpt-4o') || model.includes('claude-3') || model.includes('gemini-1.5')) {
          defaultMaxTokens = 4096
        } else if (model.includes('gpt-4o-mini') || model.includes('flash')) {
          defaultMaxTokens = 2048
        } else {
          defaultMaxTokens = 1024
        }
        
        maxTokensInput.value = defaultMaxTokens
      }
    }

    // Update cost based on model
    if (this.hasCostInputTarget) {
      const costInput = this.costInputTarget
      const currentCost = costInput.value
      
      if (!currentCost || currentCost === "") {
        const defaultCosts = {
          'gpt-4o': 0.000015,
          'gpt-4o-mini': 0.000001,
          'claude-3-sonnet': 0.000015,
          'claude-3-haiku': 0.000001,
          'gemini-1.5-pro': 0.000007,
          'gemini-1.5-flash': 0.000001
        }
        
        const matchedModel = Object.keys(defaultCosts).find(key => model.includes(key))
        if (matchedModel) {
          costInput.value = defaultCosts[matchedModel]
        }
      }
    }
  }

  // Update temperature visualization
  updateTemperatureVisualization(temperature) {
    if (this.hasTemperatureBarTarget) {
      const percentage = (temperature * 50).toFixed(1)
      this.temperatureBarTarget.style.width = `${percentage}%`
    }
  }

  // Update priority visualization
  updatePriorityVisualization(priority) {
    if (this.hasPriorityBarTarget) {
      const percentage = (11 - priority) * 10
      this.priorityBarTarget.style.width = `${percentage}%`
    }
  }

  // Update all visualizations
  updateVisualizations() {
    if (this.hasTemperatureInputTarget) {
      const temperature = parseFloat(this.temperatureInputTarget.value) || 0.7
      this.updateTemperatureVisualization(temperature)
    }
    
    if (this.hasPriorityInputTarget) {
      const priority = parseInt(this.priorityInputTarget.value) || 5
      this.updatePriorityVisualization(priority)
    }
  }

  // Toggle API key visibility
  toggleApiKeyVisibility(event) {
    event.preventDefault()
    if (this.hasApiKeyInputTarget) {
      const input = this.apiKeyInputTarget
      const type = input.type === 'password' ? 'text' : 'password'
      input.type = type
      
      const icon = event.currentTarget.querySelector('svg')
      if (type === 'text') {
        icon.innerHTML = `
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
        `
      } else {
        icon.innerHTML = `
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
        `
      }
    }
  }

  // Test configuration
  testConfiguration(event) {
    event.preventDefault()
    
    const formData = this.gatherFormData()
    
    if (!this.validateFormData(formData)) {
      this.showFeedback("Please fill in all required fields before testing", "error")
      return
    }
    
    this.showFeedback("Testing configuration...", "info")
    
    // Simulate API test (replace with actual implementation)
    setTimeout(() => {
      const success = Math.random() > 0.3 // 70% success rate for demo
      if (success) {
        this.showFeedback("Configuration test successful! ✓", "success")
      } else {
        this.showFeedback("Configuration test failed. Please check your settings.", "error")
      }
    }, 2000)
  }

  // Save configuration
  saveConfiguration(event) {
    const formData = this.gatherFormData()
    
    if (!this.validateFormData(formData)) {
      event.preventDefault()
      this.showFeedback("Please correct the errors before saving", "error")
      return
    }
    
    if (this.hasSaveButtonTarget) {
      const button = this.saveButtonTarget
      const originalText = button.textContent
      button.textContent = "Saving..."
      button.disabled = true
      
      // Reset button after form submission
      setTimeout(() => {
        button.textContent = originalText
        button.disabled = false
      }, 3000)
    }
  }

  // Show tooltip
  showTooltip(event) {
    const tooltip = event.currentTarget.dataset.tooltip
    if (tooltip) {
      this.showFeedback(tooltip, "info", 3000)
    }
  }

  // Gather form data
  gatherFormData() {
    return {
      provider: this.hasProviderSelectTarget ? this.providerSelectTarget.value : '',
      model: this.hasModelInputTarget ? this.modelInputTarget.value : '',
      apiKey: this.hasApiKeyInputTarget ? this.apiKeyInputTarget.value : '',
      baseUrl: this.hasBaseUrlInputTarget ? this.baseUrlInputTarget.value : '',
      maxTokens: this.hasMaxTokensInputTarget ? this.maxTokensInputTarget.value : '',
      temperature: this.hasTemperatureInputTarget ? this.temperatureInputTarget.value : '',
      cost: this.hasCostInputTarget ? this.costInputTarget.value : '',
      priority: this.hasPriorityInputTarget ? this.priorityInputTarget.value : '',
      isActive: this.hasActiveCheckboxTarget ? this.activeCheckboxTarget.checked : false
    }
  }

  // Validate form data
  validateFormData(data) {
    const required = ['provider', 'model', 'maxTokens', 'temperature', 'cost', 'priority']
    return required.every(field => data[field] && data[field].toString().trim() !== '')
  }

  // Validate individual fields
  validateApiKey(value) {
    // API key validation logic
    return value.length === 0 || value.length >= 20
  }

  validateBaseUrl(value) {
    if (!value) return true
    try {
      new URL(value)
      return true
    } catch {
      return false
    }
  }

  validateMaxTokens(value) {
    const num = parseInt(value)
    return num > 0 && num <= 32000
  }

  validateCost(value) {
    const num = parseFloat(value)
    return num >= 0
  }

  // Overall form validation
  validateForm() {
    const formData = this.gatherFormData()
    const isValid = this.validateFormData(formData)
    
    if (this.hasSaveButtonTarget) {
      this.saveButtonTarget.disabled = !isValid
    }
    
    return isValid
  }

  // Show feedback messages
  showFeedback(message, type = "info", duration = 5000) {
    const feedback = document.createElement("div")
    feedback.className = `fixed top-4 right-4 px-4 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 ${
      type === "success" ? "bg-green-100 text-green-800 border border-green-200" :
      type === "error" ? "bg-red-100 text-red-800 border border-red-200" :
      "bg-blue-100 text-blue-800 border border-blue-200"
    }`
    feedback.textContent = message
    
    document.body.appendChild(feedback)
    
    setTimeout(() => {
      feedback.classList.add("translate-y-2", "opacity-0")
      setTimeout(() => {
        if (feedback.parentNode) {
          feedback.parentNode.removeChild(feedback)
        }
      }, 300)
    }, duration)
  }
}
