require 'rails_helper'

RSpec.describe "Admin AI Provider Configuration Edit Page", type: :system do
  let(:tenant) { create(:tenant) }
  let(:admin_user) { create(:user, :admin, tenant: tenant) }
  let(:ai_provider_configuration) { create(:ai_provider_configuration, tenant: tenant, provider_name: "openai", ai_model_name: "gpt-4o") }

  before do
    ActsAsTenant.current_tenant = tenant
    sign_in admin_user
  end

  describe "page layout and design" do
    it "displays the modern redesigned edit form" do
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      # Check for modern header section
      expect(page).to have_content("Edit #{ai_provider_configuration.provider_name.titleize} Configuration")
      expect(page).to have_content(ai_provider_configuration.ai_model_name)
      
      # Check for status indicators
      expect(page).to have_css('.bg-green-400, .bg-red-400') # Status indicator dots
      
      # Check for form sections
      expect(page).to have_content("Provider & Model Settings")
      expect(page).to have_content("API Credentials & Connection")
      expect(page).to have_content("Model Parameters")
      expect(page).to have_content("Advanced Settings")
      expect(page).to have_content("Save Configuration")
      expect(page).to have_content("Danger Zone")
    end

    it "displays all form fields correctly" do
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      # Provider & Model fields
      expect(page).to have_field("ai_provider_configuration_provider_name")
      expect(page).to have_field("ai_provider_configuration_ai_model_name")
      
      # API Credentials fields
      expect(page).to have_field("ai_provider_configuration_api_key")
      expect(page).to have_field("ai_provider_configuration_base_url")
      
      # Model Parameters fields
      expect(page).to have_field("ai_provider_configuration_max_tokens")
      expect(page).to have_field("ai_provider_configuration_temperature")
      expect(page).to have_field("ai_provider_configuration_cost_per_token")
      expect(page).to have_field("ai_provider_configuration_priority")
      
      # Advanced Settings
      expect(page).to have_field("ai_provider_configuration_is_active")
      expect(page).to have_content("Supported Task Types")
    end

    it "shows current configuration values" do
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      # Check that current values are populated
      expect(page).to have_select("ai_provider_configuration_provider_name", selected: ai_provider_configuration.provider_name.titleize)
      expect(page).to have_field("ai_provider_configuration_ai_model_name", with: ai_provider_configuration.ai_model_name)
      expect(page).to have_field("ai_provider_configuration_max_tokens", with: ai_provider_configuration.max_tokens.to_s)
      expect(page).to have_field("ai_provider_configuration_temperature", with: ai_provider_configuration.temperature.to_s)
    end

    it "displays helpful tooltips and descriptions" do
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      # Check for tooltip buttons (info icons)
      expect(page).to have_css('button[data-tooltip]', count: 8) # Should have multiple tooltip buttons
      
      # Check for field descriptions
      expect(page).to have_content("Choose your preferred AI service provider")
      expect(page).to have_content("Enter the specific model identifier")
      expect(page).to have_content("API key will be encrypted and stored securely")
      expect(page).to have_content("Used for cost tracking and budget management")
    end
  end

  describe "form functionality" do
    it "has working form submission" do
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      # Update some fields
      fill_in "ai_provider_configuration_ai_model_name", with: "gpt-4o-mini"
      fill_in "ai_provider_configuration_max_tokens", with: "2048"
      
      # Submit the form
      click_button "Update Configuration"
      
      # Should redirect to show page
      expect(current_path).to eq(admin_ai_provider_configuration_path(ai_provider_configuration))
      expect(page).to have_content("successfully updated")
    end

    it "displays validation errors appropriately" do
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      # Clear required fields
      fill_in "ai_provider_configuration_ai_model_name", with: ""
      fill_in "ai_provider_configuration_max_tokens", with: ""
      
      # Submit the form
      click_button "Update Configuration"
      
      # Should show validation errors
      expect(page).to have_content("Please correct the following")
      expect(page).to have_content("error")
    end

    it "has working cancel functionality" do
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      # Click cancel
      click_link "Cancel"
      
      # Should redirect to show page
      expect(current_path).to eq(admin_ai_provider_configuration_path(ai_provider_configuration))
    end

    it "has working test configuration button" do
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      # Should have test configuration buttons
      test_buttons = page.all("button", text: "Test Configuration")
      expect(test_buttons.count).to be >= 1
    end
  end

  describe "interactive features" do
    it "shows visual indicators for form sections", js: true do
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      # Check for section icons
      expect(page).to have_css('svg', count: 10) # Should have multiple SVG icons
    end

    it "displays task type checkboxes correctly" do
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      # Check for task type checkboxes
      expect(page).to have_field("ai_provider_configuration[task_types][]", count: 6) # Should have 6 task types
      
      # Check for task type descriptions
      expect(page).to have_content("Generate marketing emails and newsletters")
      expect(page).to have_content("Create social media posts and content")
      expect(page).to have_content("Write blog posts and articles")
    end
  end

  describe "responsive design" do
    it "works on mobile viewport", js: true do
      page.driver.browser.manage.window.resize_to(375, 667) # iPhone SE size
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      # Page should still be functional on mobile
      expect(page).to have_content("Edit #{ai_provider_configuration.provider_name.titleize} Configuration")
      expect(page).to have_button("Update Configuration")
    end
  end

  describe "accessibility" do
    it "has proper form labels and structure" do
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      # Should have proper form labels
      expect(page).to have_css('label[for]', count: 8) # Should have multiple labeled fields
      
      # Should have proper heading hierarchy
      expect(page).to have_css('h1')
      expect(page).to have_css('h3')
    end

    it "has proper ARIA attributes" do
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      # Should have breadcrumb navigation with proper ARIA
      expect(page).to have_css('nav[aria-label="Breadcrumb"]')
    end
  end

  describe "breadcrumb navigation" do
    it "shows proper breadcrumb navigation" do
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      expect(page).to have_link("AI Provider Configurations")
      expect(page).to have_link(ai_provider_configuration.provider_name.titleize)
      expect(page).to have_content("Edit Configuration")
    end

    it "breadcrumb links work correctly" do
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      click_link "AI Provider Configurations"
      expect(current_path).to eq(admin_ai_provider_configurations_path)
    end
  end

  describe "danger zone" do
    it "displays delete configuration section" do
      visit edit_admin_ai_provider_configuration_path(ai_provider_configuration)

      expect(page).to have_content("Danger Zone")
      expect(page).to have_content("Permanently delete this AI provider configuration")
      expect(page).to have_button("Delete Configuration")
    end
  end
end
